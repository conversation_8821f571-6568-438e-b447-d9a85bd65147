# <类型>: <简短描述> (不超过50个字符)
# |
# |   类型可以是:
# |     feat     (新功能)
# |     fix      (错误修复)
# |     perf     (性能优化)
# |     refactor (代码重构，不改变功能)
# |     style    (代码风格调整, 如格式化)
# |     docs     (文档更新)
# |     test     (添加测试用例)
# |     chore    (构建过程或辅助工具的变动)
# |
# 描述详情 (可选，将会显示在简短描述下方)
# - 具体修改的内容
# - 修改的原因
# - 影响范围

# 相关问题/PR编号 (可选)
# Fixes: #123
# Relates: #456

# 备注 (可选)
# - 特殊说明
# - 注意事项
# - 兼容性信息 